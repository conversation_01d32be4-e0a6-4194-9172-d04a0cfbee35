// K12管理后台 - 后端应用
const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const compression = require('compression')
const rateLimit = require('express-rate-limit')
const path = require('path')
require('dotenv').config({ path: path.join(__dirname, '../../.env') })

const app = express()
const PORT = process.env.PORT || 8081

// 确保上传目录存在
const fs = require('fs')
const uploadsDir = path.join(__dirname, '../uploads')
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true })
}
const tempDir = path.join(uploadsDir, 'temp')
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true })
}

// 安全中间件
app.use(helmet({
  crossOriginEmbedderPolicy: false
}))

// CORS配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:8080',  // 修正端口
  credentials: true
}))

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 限制每个IP 1000次请求
  message: {
    success: false,
    error: 'API调用频率超限，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // 跳过文件上传相关的路径
  skip: (req) => {
    return req.path.includes('/batch/upload') || req.path.includes('/upload')
  }
})
app.use('/api/', limiter)

// 基础中间件
app.use(compression())
app.use(express.json({ limit: '100mb' }))  // 增加到100MB
app.use(express.urlencoded({ extended: true, limit: '100mb' }))  // 增加到100MB

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.path}`)
  next()
})

// 静态文件服务（生产环境下提供前端文件）
if (process.env.NODE_ENV === 'production') {
  const frontendPath = path.join(__dirname, '../../frontend/dist')
  app.use(express.static(frontendPath))

  // SPA路由支持
  app.get('*', (req, res, next) => {
    if (req.path.startsWith('/api/')) {
      return next()
    }
    res.sendFile(path.join(frontendPath, 'index.html'))
  })
}

// 路由
app.use('/api/config', require('./routes/config'))
app.use('/api/files', require('./routes/files'))
app.use('/api/feedback', require('./routes/feedback'))

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'K12管理后台服务运行正常',
    timestamp: new Date().toISOString(),
    env: process.env.CLOUDBASE_ENV
  })
})

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'K12教育资源管理后台 API',
    version: '1.0.0',
    endpoints: {
      config: '/api/config',
      files: '/api/files',
      feedback: '/api/feedback',
      health: '/health'
    }
  })
})

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: '接口不存在',
    path: req.originalUrl
  })
})

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error)
  res.status(500).json({
    success: false,
    error: '服务器内部错误',
    message: process.env.NODE_ENV === 'development' ? error.message : '请联系管理员'
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log('🚀 K12管理后台服务启动成功!')
  console.log(`   服务地址: http://localhost:${PORT}`)
  console.log(`   环境: ${process.env.NODE_ENV || 'development'}`)
  console.log(`   云开发环境: ${process.env.CLOUDBASE_ENV}`)
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
})

module.exports = app
