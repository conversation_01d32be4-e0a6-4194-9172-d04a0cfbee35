<template>
  <el-dialog
    v-model="visible"
    title="AI批量上传"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="json-upload-dialog">
      <!-- 使用说明 -->
      <el-alert
        title="使用AI助手批量上传文件"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="usage-tips">
            <p>📋 <strong>操作步骤：</strong></p>
            <ol>
              <li>点击"下载AI助手"获取完整的AI提示词和操作指南</li>
              <li>将提示词复制给AI助手（如ChatGPT、Claude等）</li>
              <li>上传您的教育资源文件给AI分析</li>
              <li>将AI生成的JSON数据保存为.json文件</li>
              <li>在下方上传JSON文件完成批量导入</li>
            </ol>
          </div>
        </template>
      </el-alert>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :auto-upload="false"
          :limit="1"
          accept=".json"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将JSON文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传JSON格式文件，且不超过10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 验证结果 -->
      <div v-if="validationResult" class="validation-result">
        <el-alert
          :title="validationResult.success ? '验证通过' : '验证失败'"
          :type="validationResult.success ? 'success' : 'error'"
          show-icon
          :closable="false"
        >
          <template #default>
            <div v-if="validationResult.success">
              <p>✅ JSON格式正确，共 {{ validationResult.summary?.total || 0 }} 条记录</p>
              <p>✅ 数据验证通过，可以开始上传</p>
            </div>
            <div v-else>
              <p>❌ {{ validationResult.error || '验证失败' }}</p>
              <div v-if="validationResult.errors && validationResult.errors.length > 0">
                <p><strong>错误详情：</strong></p>
                <ul class="error-list">
                  <li v-for="(error, index) in validationResult.errors.slice(0, 5)" :key="index">
                    {{ error }}
                  </li>
                  <li v-if="validationResult.errors.length > 5">
                    ... 还有 {{ validationResult.errors.length - 5 }} 个错误
                  </li>
                </ul>
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <el-progress
          :percentage="uploadProgress"
          :status="uploadProgress === 100 ? 'success' : ''"
        >
          <template #default="{ percentage }">
            <span class="progress-text">{{ percentage }}%</span>
          </template>
        </el-progress>
        <p class="progress-info">{{ progressInfo }}</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="validateJson"
          :loading="validating"
          :disabled="!selectedFile"
        >
          验证JSON
        </el-button>
        <el-button 
          type="success" 
          @click="uploadJson"
          :loading="uploading"
          :disabled="!validationResult?.success"
        >
          开始上传
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { fileApi } from '@/api/files'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const uploadRef = ref()
const fileList = ref([])
const selectedFile = ref(null)
const validating = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const progressInfo = ref('')
const validationResult = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    resetDialog()
  }
})

// 重置对话框状态
const resetDialog = () => {
  fileList.value = []
  selectedFile.value = null
  validationResult.value = null
  uploadProgress.value = 0
  progressInfo.value = ''
  validating.value = false
  uploading.value = false
}

// 处理文件选择
const handleFileChange = (file) => {
  console.log('文件选择:', file)
  selectedFile.value = file
  validationResult.value = null
}

// 处理文件数量超限
const handleExceed = () => {
  ElMessage.warning('只能上传一个JSON文件')
}

// 验证JSON文件
const validateJson = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择JSON文件')
    return
  }

  validating.value = true
  progressInfo.value = '正在验证JSON格式和数据...'

  try {
    const formData = new FormData()
    formData.append('jsonFile', selectedFile.value.raw)
    formData.append('validateOnly', 'true')

    const result = await fileApi.uploadJson(formData)
    validationResult.value = result

    if (result.success) {
      ElMessage.success('JSON验证通过')
    } else {
      ElMessage.error('JSON验证失败')
    }
  } catch (error) {
    console.error('验证失败:', error)
    validationResult.value = {
      success: false,
      error: error.message || '验证失败'
    }
    ElMessage.error('验证失败: ' + error.message)
  } finally {
    validating.value = false
    progressInfo.value = ''
  }
}

// 上传JSON文件
const uploadJson = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择JSON文件')
    return
  }

  if (!validationResult.value?.success) {
    ElMessage.warning('请先验证JSON文件')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要上传 ${validationResult.value.summary?.total || 0} 条记录吗？`,
      '确认上传',
      {
        type: 'warning'
      }
    )
  } catch {
    return
  }

  uploading.value = true
  uploadProgress.value = 0
  progressInfo.value = '开始上传...'

  try {
    const formData = new FormData()
    formData.append('jsonFile', selectedFile.value.raw)
    formData.append('validateOnly', 'false')

    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10
        progressInfo.value = `正在处理文件... ${Math.round(uploadProgress.value)}%`
      }
    }, 500)

    const result = await fileApi.uploadJson(formData)

    clearInterval(progressInterval)
    uploadProgress.value = 100
    progressInfo.value = '上传完成'

    if (result.success) {
      ElMessage.success(`批量上传成功: ${result.summary?.success || 0} 个文件`)
      emit('success')
      handleClose()
    } else {
      ElMessage.error('上传失败: ' + result.error)
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败: ' + error.message)
  } finally {
    uploading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (uploading.value) {
    ElMessage.warning('正在上传中，请稍候...')
    return
  }
  visible.value = false
}
</script>

<style scoped>
.json-upload-dialog {
  padding: 20px 0;
}

.usage-tips {
  margin-top: 10px;
}

.usage-tips ol {
  margin: 10px 0;
  padding-left: 20px;
}

.usage-tips li {
  margin: 5px 0;
  line-height: 1.5;
}

.upload-section {
  margin: 20px 0;
}

.upload-dragger {
  width: 100%;
}

.validation-result {
  margin: 20px 0;
}

.error-list {
  margin: 10px 0;
  padding-left: 20px;
  max-height: 200px;
  overflow-y: auto;
}

.error-list li {
  margin: 5px 0;
  color: #f56c6c;
  font-size: 14px;
}

.upload-progress {
  margin: 20px 0;
}

.progress-text {
  font-weight: bold;
}

.progress-info {
  text-align: center;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
