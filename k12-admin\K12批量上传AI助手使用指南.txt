你是一个专业的K12教育资源分析专家。我需要你帮我分析上传的教育资源文件，并生成标准的JSON格式数据用于批量导入系统。

## 📋 任务要求

请仔细阅读每个文件的内容，分析文件的教育属性，然后按照以下JSON格式生成数据：

### JSON数据结构：
```json
[
  {
    "file_path": "[FILE_PATH]\文件名.扩展名",
    "title": "吸引人的标题（12-25字）",
    "description": "有吸引力的描述（25-60字）",
    "category": "分类选项",
    "grade": "年级选项",
    "subject": "科目选项",
    "volume": "册别选项",
    "section": "板块选项",
    "tags": ["标签1", "标签2", "标签3"],
    "features": ["特征1", "特征2", "特征3"],
    "status": "active",
    "sort_order": 0,
    "ad_required_count": 1,
    "download_count": 随机数值,
    "view_count": 随机数值
  }
]
```

### 字段说明：

1. **file_path** - 统一使用占位符格式：`[FILE_PATH]\文件名.扩展名`
   - ⚠️ 重要：AI生成时使用占位符，用户后续替换为实际Windows路径
2. **title** - 生成吸引人的标题（12-25字），突出价值和效果
   - 使用吸引词汇：精品、必备、提分、冲刺、满分、重点、名师等
   - 示例：`【精品】一年级数学上册必备练习册`、`小升初语文冲刺满分秘籍`
3. **description** - 生成有吸引力的描述（25-60字），激发下载欲望
4. **category** - ⚠️ 重要！先判断分类，再选择年级和板块：
   - `regular`（常规资料）：仅限一年级到六年级的日常教学资料
   - `upgrade`（升学专区）：仅限幼升小和小升初的升学资料
5. **grade** - 根据category选择对应年级：
   - 常规资料：`一年级,二年级,三年级,四年级,五年级,六年级`
   - 升学专区：`幼升小,小升初`
6. **subject** - 从以下选项中选择：`语文,数学,英语,科学,音乐,美术,体育`
7. **volume** - 从以下选项中选择：`上册,下册,全册`
8. **section** - ⚠️ 根据category选择对应板块：
   - 常规资料(regular)：`单元同步,单元知识点,核心知识点,试卷,专项练习`
   - 幼升小(upgrade)：`拼音启蒙,认识数字,习惯养成,学科启蒙,知识科普`
   - 小升初(upgrade)：`语文冲刺,数学冲刺,英语强化,真题模拟,面试准备`
9. **tags** - 从以下分类中选择2-5个，作为数组：
   - 推荐等级：`重点推荐,热门下载,精品资源,新上传,编辑推荐`
   - 学习阶段：`期末复习,期中复习,单元测试,日常练习,假期作业,开学准备`
   - 难度等级：`基础练习,提高练习,培优练习,竞赛练习,奥数专题`
   - 内容类型：`练习册,试卷集,教学教案,课件资料,知识总结,作文素材`
10. **features** - 从以下选项中选择2-4个，作为数组：
    - 文件质量：`高清版,彩色版,黑白版`
    - 文件功能：`可打印,含答案,含解析`
    - 多媒体：`配套音频,配套视频`
11. **其他字段**：
    - status: 固定值 `"active"`
    - sort_order: 固定值 `0`
    - ad_required_count: 固定值 `1`
    - download_count: 随机生成 0-100
    - view_count: 随机生成 300-1000


### ⚠️ 重要分类规则：
1. **分类判断优先**：必须先判断文件属于哪个分类，再选择对应的年级和板块
2. **常规资料(regular)**：
   - 年级范围：一年级、二年级、三年级、四年级、五年级、六年级
   - 板块选择：单元同步、单元知识点、核心知识点、试卷、专项练习
3. **升学专区(upgrade)**：
   - 年级范围：幼升小、小升初
   - 幼升小板块：拼音启蒙、认识数字、习惯养成、学科启蒙、知识科普
   - 小升初板块：语文冲刺、数学冲刺、英语强化、真题模拟、面试准备
4. **严禁混用**：常规资料不能使用升学专区的板块，升学专区不能使用常规资料的年级
5. **JSON格式**：确保语法正确，所有字符串用双引号，数组格式正确

### 示例输出：
```json
[
  {
    "file_path": "[FILE_PATH]\一年级数学练习册.pdf",
    "title": "【精品】一年级数学上册必备练习册",
    "description": "同步教材重点知识，配套详细解析答案，助力孩子轻松掌握数学基础，期末考试稳拿高分",
    "category": "regular",
    "grade": "一年级",
    "subject": "数学",
    "volume": "上册",
    "section": "单元同步",
    "tags": ["基础练习", "日常练习", "重点推荐"],
    "features": ["高清版", "可打印", "含答案"],
    "status": "active",
    "sort_order": 0,
    "ad_required_count": 1,
    "download_count": 45,
    "view_count": 678
  }
]
```

现在请开始分析我上传的文件，并按照以上要求生成JSON数据。

