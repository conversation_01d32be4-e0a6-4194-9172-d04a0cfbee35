// K12 Admin 网络诊断工具
const axios = require('axios')
const { exec } = require('child_process')
const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 检查端口占用
function checkPort(port) {
  return new Promise((resolve) => {
    exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
      if (error || !stdout) {
        resolve({ port, occupied: false, processes: [] })
      } else {
        const lines = stdout.trim().split('\n')
        const processes = lines.map(line => {
          const parts = line.trim().split(/\s+/)
          return {
            protocol: parts[0],
            localAddress: parts[1],
            foreignAddress: parts[2],
            state: parts[3],
            pid: parts[4]
          }
        })
        resolve({ port, occupied: true, processes })
      }
    })
  })
}

// 测试HTTP连接
async function testHttpConnection(url, timeout = 5000) {
  try {
    const response = await axios.get(url, { timeout })
    return {
      url,
      success: true,
      status: response.status,
      statusText: response.statusText,
      data: response.data
    }
  } catch (error) {
    return {
      url,
      success: false,
      error: error.message,
      code: error.code,
      status: error.response?.status
    }
  }
}

// 测试文件上传
async function testFileUpload() {
  try {
    // 创建测试JSON文件
    const testData = [
      {
        "file_path": "C:\\test\\test.pdf",
        "title": "测试文件",
        "description": "网络诊断测试",
        "grade": "一年级",
        "subject": "语文",
        "volume": "上册",
        "section": "测试",
        "category": "regular",
        "tags": ["测试"],
        "features": ["测试"],
        "status": "active",
        "sort_order": 0,
        "ad_required_count": 1,
        "download_count": 0,
        "view_count": 0
      }
    ]
    
    const testFilePath = path.join(__dirname, 'test-upload.json')
    fs.writeFileSync(testFilePath, JSON.stringify(testData, null, 2))
    
    const FormData = require('form-data')
    const form = new FormData()
    form.append('jsonFile', fs.createReadStream(testFilePath))
    form.append('validateOnly', 'true')
    
    const response = await axios.post('http://localhost:8081/api/files/batch/upload', form, {
      headers: {
        ...form.getHeaders(),
        'Content-Type': 'multipart/form-data'
      },
      timeout: 30000
    })
    
    // 清理测试文件
    fs.unlinkSync(testFilePath)
    
    return {
      success: true,
      status: response.status,
      data: response.data
    }
  } catch (error) {
    // 清理测试文件
    const testFilePath = path.join(__dirname, 'test-upload.json')
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath)
    }
    
    return {
      success: false,
      error: error.message,
      code: error.code,
      status: error.response?.status,
      data: error.response?.data
    }
  }
}

// 检查云开发连接
async function testCloudbaseConnection() {
  try {
    require('dotenv').config()
    const cloudbase = require('@cloudbase/node-sdk')
    
    const app = cloudbase.init({
      env: process.env.CLOUDBASE_ENV,
      secretId: process.env.CLOUDBASE_SECRET_ID,
      secretKey: process.env.CLOUDBASE_SECRET_KEY
    })
    
    const db = app.database()
    const result = await db.collection('files').limit(1).get()
    
    return {
      success: true,
      env: process.env.CLOUDBASE_ENV,
      recordCount: result.data.length
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      code: error.code
    }
  }
}

// 主诊断函数
async function diagnose() {
  log('=== K12 Admin 网络诊断工具 ===', 'cyan')
  log('')
  
  // 1. 检查端口占用
  log('1. 检查端口占用状态...', 'blue')
  const backendPort = await checkPort(8081)
  const frontendPort = await checkPort(8080)
  
  if (backendPort.occupied) {
    log(`✅ 后端端口 8081 被占用 (${backendPort.processes.length} 个进程)`, 'green')
  } else {
    log(`❌ 后端端口 8081 未被占用`, 'red')
  }
  
  if (frontendPort.occupied) {
    log(`✅ 前端端口 8080 被占用 (${frontendPort.processes.length} 个进程)`, 'green')
  } else {
    log(`❌ 前端端口 8080 未被占用`, 'red')
  }
  
  log('')
  
  // 2. 测试HTTP连接
  log('2. 测试HTTP连接...', 'blue')
  const backendHealth = await testHttpConnection('http://localhost:8081/health')
  const backendRoot = await testHttpConnection('http://localhost:8081/')
  
  if (backendHealth.success) {
    log(`✅ 后端健康检查: ${backendHealth.status} ${backendHealth.statusText}`, 'green')
  } else {
    log(`❌ 后端健康检查失败: ${backendHealth.error}`, 'red')
  }
  
  if (backendRoot.success) {
    log(`✅ 后端根路径: ${backendRoot.status} ${backendRoot.statusText}`, 'green')
  } else {
    log(`❌ 后端根路径失败: ${backendRoot.error}`, 'red')
  }
  
  log('')
  
  // 3. 测试云开发连接
  log('3. 测试云开发连接...', 'blue')
  const cloudbaseTest = await testCloudbaseConnection()
  
  if (cloudbaseTest.success) {
    log(`✅ 云开发连接成功: 环境 ${cloudbaseTest.env}`, 'green')
    log(`   数据库记录数: ${cloudbaseTest.recordCount}`, 'blue')
  } else {
    log(`❌ 云开发连接失败: ${cloudbaseTest.error}`, 'red')
  }
  
  log('')
  
  // 4. 测试文件上传
  log('4. 测试JSON文件上传...', 'blue')
  const uploadTest = await testFileUpload()
  
  if (uploadTest.success) {
    log(`✅ JSON上传测试成功: ${uploadTest.status}`, 'green')
    log(`   响应数据: ${JSON.stringify(uploadTest.data).substring(0, 100)}...`, 'blue')
  } else {
    log(`❌ JSON上传测试失败: ${uploadTest.error}`, 'red')
    if (uploadTest.code) {
      log(`   错误代码: ${uploadTest.code}`, 'yellow')
    }
    if (uploadTest.data) {
      log(`   服务器响应: ${JSON.stringify(uploadTest.data)}`, 'yellow')
    }
  }
  
  log('')
  log('=== 诊断完成 ===', 'cyan')
  
  // 5. 问题分析和建议
  log('')
  log('📋 问题分析和建议:', 'yellow')
  
  if (!backendPort.occupied) {
    log('• 后端服务未启动，请运行: npm run dev 或 node start.js', 'yellow')
  }
  
  if (!backendHealth.success && backendPort.occupied) {
    log('• 后端服务已启动但无响应，可能存在代码错误，请检查后端日志', 'yellow')
  }
  
  if (!cloudbaseTest.success) {
    log('• 云开发连接失败，请检查 .env 文件中的云开发配置', 'yellow')
  }
  
  if (!uploadTest.success) {
    if (uploadTest.code === 'ECONNRESET') {
      log('• 连接被重置，通常是后端处理过程中崩溃，请检查后端错误日志', 'yellow')
    } else if (uploadTest.code === 'ECONNREFUSED') {
      log('• 连接被拒绝，请确认后端服务正在运行', 'yellow')
    } else if (uploadTest.code === 'ETIMEDOUT') {
      log('• 请求超时，可能是处理时间过长或网络问题', 'yellow')
    }
  }
}

// 运行诊断
diagnose().catch(console.error)
