const fs = require('fs')
const path = require('path')
const Joi = require('joi')
const uploadService = require('./uploadService')
const { db } = require('../config/cloudbase')

class JsonService {
  constructor() {
    // JSON数据验证schema
    this.fileSchema = Joi.object({
      file_path: Joi.string().required(),
      title: Joi.string().required(),
      description: Joi.string().allow('').optional(),
      grade: Joi.string().valid('幼升小', '一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '小升初').required(),
      subject: Joi.string().valid('语文', '数学', '英语', '科学', '音乐', '美术', '体育').required(),
      volume: Joi.string().valid('上册', '下册', '全册').required(),
      section: Joi.string().required(),
      category: Joi.string().valid('regular', 'upgrade').default('regular'),
      tags: Joi.array().items(Joi.string()).default([]),
      features: Joi.array().items(Joi.string()).default([]),
      status: Joi.string().valid('active', 'inactive').default('active'),
      sort_order: Joi.number().integer().default(0),
      ad_required_count: Joi.number().integer().min(1).default(1),
      download_count: Joi.number().integer().min(0).default(0),
      view_count: Joi.number().integer().min(0).default(0)
    })

    // 有效的标签选项
    this.validTags = [
      // 内容完整性
      '完整版', '精简版', '重点版',
      // 推荐等级
      '重点推荐', '热门下载', '精品资源', '新上传', '编辑推荐',
      // 学习阶段
      '期末复习', '期中复习', '单元测试', '日常练习', '假期作业', '开学准备',
      // 难度等级
      '基础练习', '提高练习', '培优练习', '竞赛练习', '奥数专题',
      // 内容类型
      '练习册', '试卷集', '教学教案', '课件资料', '知识总结', '作文素材', '阅读理解', '计算专项', '应用题集',
      // 学习方法
      '学习方法', '解题技巧', '记忆口诀', '错误分析',
      // 特殊用途
      '家长辅导', '预习资料', '复习资料', '拓展阅读'
    ]

    // 有效的文件特征选项
    this.validFeatures = [
      // 文件质量
      '高清版', '彩色版', '黑白版',
      // 文件功能
      '可打印', '含答案', '含解析',
      // 多媒体
      '配套音频', '配套视频'
    ]
  }

  // 解析JSON文件
  async parseJsonFile(jsonFilePath) {
    try {
      console.log('开始解析JSON文件:', jsonFilePath)

      // 检查文件是否存在
      if (!fs.existsSync(jsonFilePath)) {
        throw new Error('JSON文件不存在')
      }

      // 读取文件内容
      let fileContent
      try {
        fileContent = fs.readFileSync(jsonFilePath, 'utf8')
      } catch (readError) {
        throw new Error('读取JSON文件失败: ' + readError.message)
      }

      if (!fileContent || fileContent.trim() === '') {
        throw new Error('JSON文件内容为空')
      }

      console.log('文件内容长度:', fileContent.length)
      console.log('原始JSON内容预览:', fileContent.substring(0, 300) + '...')

      // 处理Windows路径：将单反斜杠转换为双反斜杠（JSON转义）
      let needsPathFix = false
      try {
        // 先尝试直接解析，如果失败再处理路径
        const testParse = JSON.parse(fileContent)
        console.log('✅ 首次JSON解析成功')
        if (testParse.length > 0) {
          console.log('第一条记录的file_path:', testParse[0].file_path)
        }
      } catch (firstParseError) {
        console.log('❌ 首次JSON解析失败，尝试修复Windows路径格式:', firstParseError.message)
        needsPathFix = true

        // 匹配 "file_path": "C:\..." 格式并转义反斜杠
        fileContent = fileContent.replace(/"file_path":\s*"([^"]*?)"/g, (match, path) => {
          // 只有当路径包含未转义的反斜杠时才处理
          if (path.includes('\\') && !path.includes('\\\\')) {
            const escapedPath = path.replace(/\\/g, '\\\\')
            console.log(`🔧 路径转义: ${path} -> ${escapedPath}`)
            return `"file_path": "${escapedPath}"`
          }
          return match
        })
      }

      if (needsPathFix) {
        console.log('处理后的JSON内容预览:', fileContent.substring(0, 300) + '...')
      }

      // 解析JSON
      let jsonData
      try {
        jsonData = JSON.parse(fileContent)
        console.log('✅ JSON解析成功!')
        console.log('解析结果类型:', Array.isArray(jsonData) ? 'Array' : typeof jsonData)
        console.log('数据条数:', Array.isArray(jsonData) ? jsonData.length : 1)

        // 详细检查每条记录的file_path
        if (Array.isArray(jsonData)) {
          jsonData.forEach((item, index) => {
            console.log(`记录${index + 1} file_path:`, item.file_path)
            console.log(`记录${index + 1} title:`, item.title)
          })
        }
      } catch (parseError) {
        console.error('❌ JSON解析失败:', parseError.message)
        console.error('错误位置:', parseError.message.match(/position (\d+)/)?.[1] || '未知')
        return {
          success: false,
          error: 'JSON格式错误: ' + parseError.message,
          data: null
        }
      }

      // 确保是数组格式
      if (!Array.isArray(jsonData)) {
        return {
          success: false,
          error: 'JSON数据必须是数组格式',
          data: null
        }
      }

      console.log(`解析到 ${jsonData.length} 条记录`)

      return {
        success: true,
        data: jsonData,
        count: jsonData.length
      }
    } catch (error) {
      console.error('解析JSON文件失败:', error)
      return {
        success: false,
        error: error.message,
        data: null
      }
    }
  }

  // 验证JSON数据
  validateJsonData(data) {
    const errors = []
    const validData = []

    data.forEach((row, index) => {
      const rowErrors = []

      // 基础字段验证
      const { error, value } = this.fileSchema.validate(row, { allowUnknown: false })
      if (error) {
        rowErrors.push(`第${index + 1}行: ${error.details[0].message}`)
      } else {
        // 验证标签
        if (value.tags && value.tags.length > 0) {
          const invalidTags = value.tags.filter(tag => !this.validTags.includes(tag))
          if (invalidTags.length > 0) {
            rowErrors.push(`第${index + 1}行: 无效的标签: ${invalidTags.join(', ')}`)
          }
        }

        // 验证文件特征
        if (value.features && value.features.length > 0) {
          const invalidFeatures = value.features.filter(feature => !this.validFeatures.includes(feature))
          if (invalidFeatures.length > 0) {
            rowErrors.push(`第${index + 1}行: 无效的文件特征: ${invalidFeatures.join(', ')}`)
          }
        }

        // 详细检查file_path
        console.log(`🔍 检查第${index + 1}行 file_path:`, value.file_path)
        console.log(`🔍 检查第${index + 1}行 title:`, value.title)

        // 验证分类和年级的匹配规则
        if (value.category === 'upgrade' && !['幼升小', '小升初'].includes(value.grade)) {
          rowErrors.push(`第${index + 1}行: 升学专区(upgrade)只能是幼升小或小升初`)
        }

        if (value.category === 'regular' && !['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'].includes(value.grade)) {
          rowErrors.push(`第${index + 1}行: 常规资料(regular)只能是一年级到六年级`)
        }

        // 验证板块和分类的匹配规则
        const regularSections = ['单元同步', '单元知识点', '核心知识点', '试卷', '专项练习']
        const upgradeSections = ['拼音启蒙', '认识数字', '习惯养成', '学科启蒙', '知识科普', '语文冲刺', '数学冲刺', '英语强化', '真题模拟', '面试准备']

        if (value.category === 'regular' && !regularSections.includes(value.section)) {
          rowErrors.push(`第${index + 1}行: 常规资料的板块只能是: ${regularSections.join(', ')}`)
        }

        if (value.category === 'upgrade' && !upgradeSections.includes(value.section)) {
          rowErrors.push(`第${index + 1}行: 升学专区的板块只能是: ${upgradeSections.join(', ')}`)
        }

        // 验证文件路径（支持占位符格式和实际路径）
        const isPlaceholderPath = value.file_path.startsWith('[FILE_PATH]/')
        const isWindowsPath = /^[A-Za-z]:\\/.test(value.file_path)
        const isUnixPath = value.file_path.startsWith('/')

        if (!isPlaceholderPath && !isWindowsPath && !isUnixPath) {
          rowErrors.push(`第${index + 1}行: 文件路径格式不正确，支持格式：[FILE_PATH]/文件名 或 实际文件路径`)
        }

        if (rowErrors.length === 0) {
          validData.push(value)
        }
      }

      if (rowErrors.length > 0) {
        errors.push(...rowErrors)
      }
    })

    return {
      valid: errors.length === 0,
      errors,
      validData,
      totalRows: data.length,
      validRows: validData.length,
      errorRows: errors.length
    }
  }

  // 仅验证JSON文件
  async validateJsonFile(jsonFilePath) {
    try {
      console.log('开始验证JSON文件:', jsonFilePath)

      // 解析JSON文件
      const parseResult = await this.parseJsonFile(jsonFilePath)
      if (!parseResult.success) {
        console.error('JSON解析失败:', parseResult.error)
        return parseResult
      }

      console.log('JSON解析成功，开始数据验证')

      // 验证数据
      const validation = this.validateJsonData(parseResult.data)

      console.log('数据验证完成:', {
        valid: validation.valid,
        totalRows: validation.totalRows,
        validRows: validation.validRows,
        errorRows: validation.errorRows
      })

      return {
        success: validation.valid,
        message: validation.valid ? '数据验证通过' : '数据验证失败',
        errors: validation.errors,
        summary: {
          total: validation.totalRows,
          valid: validation.validRows,
          errors: validation.errorRows
        }
      }
    } catch (error) {
      console.error('验证JSON文件失败:', error)
      console.error('错误堆栈:', error.stack)
      return {
        success: false,
        error: '验证失败: ' + error.message
      }
    }
  }



  // 批量处理JSON上传（保存元数据到数据库）
  async processJsonUpload(jsonFilePath, progressCallback) {
    console.log('=== processJsonUpload 开始 ===')
    try {
      console.log('开始处理JSON批量上传，文件路径:', jsonFilePath)

      // 解析JSON文件
      console.log('步骤1: 解析JSON文件')
      const parseResult = await this.parseJsonFile(jsonFilePath)
      console.log('JSON解析结果:', parseResult.success ? '成功' : '失败')
      if (!parseResult.success) {
        console.log('JSON解析失败，返回错误')
        return parseResult
      }

      // 验证数据
      console.log('步骤2: 验证数据')
      const validation = this.validateJsonData(parseResult.data)
      console.log('数据验证结果:', validation.valid ? '通过' : '失败')
      if (!validation.valid) {
        console.log('数据验证失败，返回错误')
        return {
          success: false,
          error: '数据验证失败',
          errors: validation.errors,
          summary: {
            total: validation.totalRows,
            valid: validation.validRows,
            errors: validation.errorRows
          }
        }
      }

      console.log('数据验证通过，开始保存到数据库')

      // 测试数据库连接
      try {
        console.log('测试数据库连接...')
        const testResult = await db.collection('files').limit(1).get()
        console.log('✅ 数据库连接正常')
      } catch (dbError) {
        console.error('❌ 数据库连接失败:', dbError)
        return {
          success: false,
          error: '数据库连接失败: ' + dbError.message,
          details: {
            code: dbError.code,
            message: dbError.message
          }
        }
      }

      // 批量保存元数据到数据库
      const saveResults = []
      const totalRecords = validation.validData.length

      for (let i = 0; i < validation.validData.length; i++) {
        const row = validation.validData[i]

        try {
          // 处理文件路径
          let fileName
          if (row.file_path.startsWith('[FILE_PATH]/')) {
            fileName = row.file_path.replace('[FILE_PATH]/', '')
          } else {
            fileName = path.basename(row.file_path)
          }

          // 准备数据库记录
          const fileRecord = {
            title: row.title,
            description: row.description || '',
            file_url: '', // JSON上传模式下，文件URL为空，需要后续手动关联
            file_path: row.file_path, // 保存原始路径信息
            original_name: fileName,
            file_type: path.extname(fileName).toLowerCase().substring(1),
            file_size: 0, // JSON模式下文件大小未知
            pages: null, // JSON模式下页数未知
            preview_images: [],
            grade: row.grade,
            subject: row.subject,
            volume: row.volume,
            section: row.section,
            category: row.category || 'regular',
            tags: row.tags || [],
            features: row.features || [],
            status: row.status || 'active',
            sort_order: row.sort_order || 0,
            ad_required_count: row.ad_required_count || 1,
            download_count: row.download_count || 0,
            view_count: row.view_count || 0,
            upload_time: new Date(),
            update_time: new Date()
          }

          console.log(`保存第${i + 1}/${totalRecords}条记录: ${row.title}`)

          // 保存到数据库
          console.log('准备保存到数据库，记录详情:', {
            title: fileRecord.title,
            file_path: fileRecord.file_path,
            grade: fileRecord.grade,
            subject: fileRecord.subject
          })

          const result = await db.collection('files').add(fileRecord)
          console.log('数据库保存成功，ID:', result.id)

          saveResults.push({
            row: i + 1,
            filename: fileName,
            title: row.title,
            success: true,
            data: { id: result.id },
            error: null
          })

          // 调用进度回调
          if (progressCallback) {
            progressCallback({
              current: i + 1,
              total: totalRecords,
              percentage: Math.round(((i + 1) / totalRecords) * 100),
              currentFile: fileName,
              currentTitle: row.title,
              success: true
            })
          }

        } catch (error) {
          console.error(`保存第${i + 1}行失败:`, error)
          console.error('错误详情:', {
            message: error.message,
            code: error.code,
            stack: error.stack,
            record: {
              title: row.title,
              file_path: row.file_path,
              grade: row.grade,
              subject: row.subject
            }
          })

          // 检查是否是网络/数据库连接问题
          let errorMessage = error.message
          if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            errorMessage = '数据库连接失败，请检查网络连接'
          } else if (error.code === 'ETIMEDOUT') {
            errorMessage = '数据库操作超时，请稍后重试'
          } else if (error.message && error.message.includes('network')) {
            errorMessage = '网络连接异常，请检查网络状态'
          }

          saveResults.push({
            row: i + 1,
            filename: row.file_path,
            title: row.title,
            success: false,
            error: errorMessage
          })
        }
      }

      // 统计结果
      const successCount = saveResults.filter(r => r.success).length
      const failCount = saveResults.filter(r => !r.success).length

      return {
        success: true,
        message: `批量导入完成: 成功${successCount}条，失败${failCount}条`,
        summary: {
          total: totalRecords,
          success: successCount,
          failed: failCount
        },
        results: saveResults
      }

    } catch (error) {
      console.error('=== processJsonUpload 异常 ===')
      console.error('处理JSON上传失败:', error)
      console.error('错误类型:', error.name)
      console.error('错误消息:', error.message)
      console.error('错误代码:', error.code)
      console.error('错误堆栈:', error.stack)
      console.error('=== processJsonUpload 异常结束 ===')

      return {
        success: false,
        error: '处理失败: ' + error.message,
        details: {
          name: error.name,
          code: error.code,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      }
    }
  }

  // 获取MIME类型
  getMimeType(extension) {
    const mimeTypes = {
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif'
    }
    return mimeTypes[extension] || 'application/octet-stream'
  }
}

module.exports = new JsonService()
