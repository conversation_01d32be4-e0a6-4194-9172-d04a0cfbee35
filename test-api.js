const FormData = require('form-data');
const fs = require('fs');
const { default: fetch } = require('node-fetch');

async function testAPI() {
  try {
    const form = new FormData();
    form.append('jsonFile', fs.createReadStream('test-upload.json'));
    form.append('validateOnly', 'true');

    const response = await fetch('http://localhost:8081/api/files/batch/upload', {
      method: 'POST',
      body: form
    });

    const result = await response.json();
    console.log('API响应:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testAPI();
