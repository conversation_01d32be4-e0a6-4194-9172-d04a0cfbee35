// 文件管理API
import axios from 'axios'

const API_BASE = '/api/files'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081',
  timeout: 30000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('API请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    const message = error.response?.data?.error || error.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

export const fileApi = {
  // 获取文件列表
  getFileList(params = {}) {
    return api.get(API_BASE, { params })
  },

  // 获取文件详情
  getFileDetail(id) {
    return api.get(`${API_BASE}/${id}`)
  },

  // 更新文件信息
  updateFile(id, data) {
    return api.put(`${API_BASE}/${id}`, data)
  },

  // 删除文件
  deleteFile(id) {
    return api.delete(`${API_BASE}/${id}`)
  },

  // 批量删除文件
  batchDeleteFiles(fileIds) {
    return api.post(`${API_BASE}/batch-delete`, { fileIds })
  },

  // 批量更新文件状态
  batchUpdateStatus(fileIds, status) {
    return api.post(`${API_BASE}/batch-status`, { fileIds, status })
  },

  // 上传单个文件
  uploadFile(file, metadata, onProgress) {
    const formData = new FormData()
    formData.append('file', file)

    // 添加元数据
    Object.keys(metadata).forEach(key => {
      if (metadata[key] !== undefined && metadata[key] !== null) {
        formData.append(key, metadata[key])
      }
    })

    return api.post(`${API_BASE}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })
  },

  // 批量上传文件
  batchUploadFiles(files, metadata, onProgress) {
    const formData = new FormData()

    // 添加文件
    files.forEach(file => {
      formData.append('files', file)
    })

    // 添加元数据
    Object.keys(metadata).forEach(key => {
      if (metadata[key] !== undefined && metadata[key] !== null) {
        formData.append(key, metadata[key])
      }
    })

    return api.post(`${API_BASE}/batch-upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })
  },

  // 获取文件下载链接
  getDownloadUrl(id) {
    return api.get(`${API_BASE}/${id}/download`)
  },

  // 诊断文件URL问题
  diagnoseFile(id) {
    return api.get(`${API_BASE}/${id}/diagnose`)
  },

  // 获取文件统计信息
  getFileStats() {
    return api.get(`${API_BASE}/stats`)
  },

  // 获取筛选选项
  getFilterOptions() {
    return api.get(`${API_BASE}/filter-options`)
  },

  // 下载批量上传模板（AI助手指南）
  downloadBatchTemplate() {
    return api.get(`${API_BASE}/batch/template`, {
      responseType: 'blob'
    })
  },

  // JSON批量上传
  uploadJson(formDataOrFile, onProgress) {
    let formData

    if (formDataOrFile instanceof FormData) {
      formData = formDataOrFile
    } else {
      // 兼容旧的调用方式
      formData = new FormData()
      formData.append('jsonFile', formDataOrFile)
    }

    return api.post(`${API_BASE}/batch/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const progress = {
            current: 1,
            total: 1,
            percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total),
            currentFile: '正在上传JSON文件...'
          }
          onProgress(progress)
        }
      }
    })
  }
}

export default fileApi
