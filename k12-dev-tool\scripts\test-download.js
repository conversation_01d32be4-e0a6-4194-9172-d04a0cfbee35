// 测试下载AI助手功能
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

async function testDownloadTemplate() {
  console.log('🧪 测试下载AI助手功能...');
  
  try {
    console.log('📥 正在下载AI助手使用指南...');
    
    const response = await axios.get(`${BASE_URL}/api/files/batch/template`, {
      responseType: 'blob'
    });
    
    console.log('✅ 下载成功！');
    console.log('📄 响应头:', response.headers);
    console.log('📊 文件大小:', response.data.length, 'bytes');
    
    // 保存到临时文件进行验证
    const tempFile = path.join(__dirname, '../temp/downloaded_guide.txt');
    
    // 确保目录存在
    const tempDir = path.dirname(tempFile);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // 写入文件
    fs.writeFileSync(tempFile, response.data);
    
    console.log('💾 文件已保存到:', tempFile);
    
    // 读取并显示前几行内容
    const content = fs.readFileSync(tempFile, 'utf8');
    const lines = content.split('\n').slice(0, 10);
    
    console.log('📖 文件内容预览:');
    console.log('================');
    lines.forEach((line, index) => {
      console.log(`${index + 1}: ${line}`);
    });
    console.log('================');
    
    console.log('🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.response) {
      console.error('📄 响应状态:', error.response.status);
      console.error('📄 响应数据:', error.response.data);
    }
  }
}

// 主函数
async function main() {
  console.log('========================================');
  console.log('K12-Admin AI助手下载功能测试');
  console.log('========================================');
  
  // 检查后端服务是否启动
  try {
    await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ 后端服务已启动');
  } catch (error) {
    console.log('❌ 后端服务未启动，请先启动后端服务');
    console.log('启动命令: cd k12-admin/backend && npm run dev');
    return;
  }

  await testDownloadTemplate();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testDownloadTemplate };
