@echo off
echo ========================================
echo K12-Admin 开发环境启动脚本
echo ========================================

echo.
echo 正在启动后端服务...
cd /d "%~dp0..\..\k12-admin\backend"
start "K12-Admin Backend" cmd /k "npm run dev"

echo.
echo 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo.
echo 正在启动前端服务...
cd /d "%~dp0..\..\k12-admin\frontend"
start "K12-Admin Frontend" cmd /k "npm run dev"

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo 后端服务: http://localhost:3000
echo 前端服务: http://localhost:5173
echo 管理后台: http://localhost:5173
echo ========================================
echo.
echo 请等待几秒钟让服务完全启动，然后访问管理后台进行测试
echo.
pause
