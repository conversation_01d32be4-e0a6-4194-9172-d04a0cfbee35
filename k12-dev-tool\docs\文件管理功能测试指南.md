# K12-Admin 文件管理功能测试指南

## 🎯 测试目标

验证新增的全选功能和时间范围筛选功能是否正常工作。

## 🧪 测试环境准备

### 启动开发环境

1. **启动后端服务**
```bash
cd k12-admin/backend
npm install
npm run dev
```

2. **启动前端服务**
```bash
cd k12-admin/frontend
npm install
npm run dev
```

3. **访问管理后台**
打开浏览器访问：`http://localhost:5173`

## 📋 测试用例

### 一、全选功能测试

#### 1.1 基础全选测试
**测试步骤**：
1. 进入文件管理页面
2. 确保页面有多个文件显示
3. 点击表格头部的全选复选框

**预期结果**：
- ✅ 当前页所有文件的复选框被选中
- ✅ 批量操作栏显示，显示选中文件数量
- ✅ 全选复选框显示为选中状态

#### 1.2 取消全选测试
**测试步骤**：
1. 在全选状态下，再次点击全选复选框

**预期结果**：
- ✅ 当前页所有文件的复选框被取消选中
- ✅ 批量操作栏隐藏
- ✅ 全选复选框显示为未选中状态

#### 1.3 部分选中状态测试
**测试步骤**：
1. 手动选中当前页的部分文件（不是全部）

**预期结果**：
- ✅ 全选复选框显示为半选状态（indeterminate）
- ✅ 批量操作栏显示选中的文件数量

#### 1.4 跨页选择测试
**测试步骤**：
1. 在第1页选中部分文件
2. 切换到第2页
3. 选中第2页的部分文件
4. 切换回第1页

**预期结果**：
- ✅ 第1页之前选中的文件仍然保持选中状态
- ✅ 批量操作栏显示所有跨页选中的文件总数
- ✅ 全选复选框根据当前页选中情况正确显示状态

#### 1.5 批量操作测试
**测试步骤**：
1. 跨页选中多个文件
2. 点击"批量删除"按钮
3. 确认删除操作

**预期结果**：
- ✅ 显示确认对话框，包含正确的选中文件数量
- ✅ 确认后，所有选中的文件被删除
- ✅ 选中状态被清空

### 二、时间范围筛选测试

#### 2.1 快捷时间选项测试

**测试步骤 - 今天**：
1. 在时间筛选下拉框中选择"今天"

**预期结果**：
- ✅ 只显示今天上传的文件
- ✅ 文件列表自动刷新
- ✅ 分页信息更新

**测试步骤 - 昨天**：
1. 在时间筛选下拉框中选择"昨天"

**预期结果**：
- ✅ 只显示昨天上传的文件

**测试步骤 - 最近7天**：
1. 在时间筛选下拉框中选择"最近7天"

**预期结果**：
- ✅ 显示过去7天内上传的文件

**测试步骤 - 最近30天**：
1. 在时间筛选下拉框中选择"最近30天"

**预期结果**：
- ✅ 显示过去30天内上传的文件

#### 2.2 自定义时间范围测试

**测试步骤**：
1. 在时间筛选下拉框中选择"自定义"
2. 在出现的日期选择器中选择开始日期和结束日期
3. 确认选择

**预期结果**：
- ✅ 选择"自定义"后，显示日期范围选择器
- ✅ 选择日期范围后，只显示该时间段内的文件
- ✅ 文件列表和分页信息正确更新

#### 2.3 清空时间筛选测试

**测试步骤**：
1. 在已设置时间筛选的情况下，点击时间筛选的清空按钮

**预期结果**：
- ✅ 时间筛选被清空
- ✅ 显示所有时间的文件
- ✅ 自定义日期选择器隐藏

#### 2.4 时间筛选与其他筛选组合测试

**测试步骤**：
1. 设置年级筛选为"一年级"
2. 设置时间筛选为"最近7天"
3. 输入关键词搜索

**预期结果**：
- ✅ 显示同时满足所有筛选条件的文件
- ✅ 各个筛选条件正确组合生效

### 三、重置筛选测试

**测试步骤**：
1. 设置多个筛选条件（包括时间筛选）
2. 点击"重置"按钮

**预期结果**：
- ✅ 所有筛选条件被清空
- ✅ 时间筛选恢复为"全部"
- ✅ 自定义日期范围被清空
- ✅ 文件列表显示所有文件

## 🐛 常见问题排查

### 问题1：全选复选框状态不正确
**可能原因**：
- 计算属性逻辑错误
- 文件ID比较问题

**排查方法**：
1. 打开浏览器开发者工具
2. 检查console是否有错误信息
3. 检查selectedFiles数组内容

### 问题2：时间筛选无效果
**可能原因**：
- 后端API参数未正确传递
- 数据库查询条件错误

**排查方法**：
1. 检查网络请求参数
2. 查看后端日志
3. 检查数据库查询语句

### 问题3：跨页选择状态丢失
**可能原因**：
- 页面切换时状态管理错误
- selectedFiles数组被意外清空

**排查方法**：
1. 检查handlePageChange方法
2. 确认selectedFiles是否正确维护

## 📊 性能测试

### 大数据量测试
**测试场景**：
- 文件总数：1000+
- 单页显示：20个文件
- 跨页选择：选中100+个文件

**性能指标**：
- ✅ 全选操作响应时间 < 100ms
- ✅ 页面切换时间 < 200ms
- ✅ 批量操作确认对话框显示时间 < 50ms

### 内存使用测试
**测试方法**：
1. 使用浏览器开发者工具监控内存使用
2. 进行大量选择/取消选择操作
3. 检查是否有内存泄漏

**预期结果**：
- ✅ 内存使用稳定，无明显泄漏
- ✅ 长时间操作后性能无明显下降

## ✅ 测试完成检查清单

### 功能测试
- [ ] 全选功能正常
- [ ] 取消全选功能正常
- [ ] 部分选中状态显示正确
- [ ] 跨页选择状态保持
- [ ] 批量操作正常执行
- [ ] 时间快捷选项正常
- [ ] 自定义时间范围正常
- [ ] 时间筛选清空正常
- [ ] 筛选条件组合正常
- [ ] 重置功能正常

### 界面测试
- [ ] 全选复选框位置正确
- [ ] 时间筛选器布局正常
- [ ] 自定义日期选择器显示/隐藏正常
- [ ] 响应式布局适配正常
- [ ] 样式显示正确

### 兼容性测试
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常
- [ ] Safari浏览器正常
- [ ] 移动端适配正常

### 性能测试
- [ ] 大数据量下性能正常
- [ ] 内存使用稳定
- [ ] 响应时间符合要求

## 📝 测试报告模板

```
测试日期：____年__月__日
测试人员：__________
测试环境：__________

功能测试结果：
✅ 全选功能：通过/失败
✅ 时间筛选功能：通过/失败

发现问题：
1. 问题描述
2. 复现步骤
3. 预期结果
4. 实际结果

建议改进：
1. 改进建议1
2. 改进建议2

总体评价：
功能完整性：优秀/良好/一般/需改进
用户体验：优秀/良好/一般/需改进
性能表现：优秀/良好/一般/需改进
```
